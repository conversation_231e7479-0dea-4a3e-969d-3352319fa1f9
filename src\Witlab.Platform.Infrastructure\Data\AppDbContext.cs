﻿using Microsoft.EntityFrameworkCore.Storage;
using Witlab.Platform.Core.Auth;
using Witlab.Platform.Core.ContributorAggregate;
using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Data;
public class AppDbContext(DbContextOptions<AppDbContext> options,
  IDomainEventDispatcher? dispatcher) : DbContext(options), ITransactionUnitOfWork
{
  //dotnet ef migrations add MigrationName -p src/Witlab.Platform.Infrastructure -s src/Witlab.Platform.Web
  //dotnet ef database update --project .\src\Witlab.Platform.Infrastructure\ --startup-project .\src\Witlab.Platform.Web\

  private readonly IDomainEventDispatcher? _dispatcher = dispatcher;

  public DbSet<Contributor> Contributors => Set<Contributor>();

  // Platform实体
  public DbSet<User> Users => Set<User>();
  public DbSet<Role> Roles => Set<Role>();
  public DbSet<Menu> Menus => Set<Menu>();
  public DbSet<Dept> Depts => Set<Dept>();
  public DbSet<UserRole> UserRoles => Set<UserRole>();
  public DbSet<UserDept> UserDepts => Set<UserDept>();
  //public DbSet<RoleMenu> RoleMenus => Set<RoleMenu>();
  public DbSet<Permission> Permissions => Set<Permission>();
  public DbSet<RolePermission> RolePermissions => Set<RolePermission>();

  // 认证相关实体
  public DbSet<RefreshToken> RefreshTokens => Set<RefreshToken>();

  public IDbContextTransaction? CurrentTransaction { get; set; }

  public virtual Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
  {
    return Database.BeginTransactionAsync(cancellationToken);
  }

  public async Task CommitAsync(CancellationToken cancellationToken = default)
  {
    if (CurrentTransaction != null)
    {
      await CurrentTransaction.CommitAsync(cancellationToken);
      CurrentTransaction = null;
    }
  }

  public async Task RollbackAsync(CancellationToken cancellationToken = default)
  {
    if (CurrentTransaction != null)
    {
      await CurrentTransaction.RollbackAsync(cancellationToken);
      CurrentTransaction = null;
    }
  }

  protected override void OnModelCreating(ModelBuilder modelBuilder)
  {
    base.OnModelCreating(modelBuilder);
    modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
  }

  public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
  {
    //int result = await base.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

    //// ignore events if no dispatcher provided
    //if (_dispatcher == null) return result;

    //// dispatch events only if save was successful
    //var entitiesWithEvents = ChangeTracker.Entries<HasDomainEventsBase>()
    //    .Select(e => e.Entity)
    //    .Where(e => e.DomainEvents.Any())
    //    .ToArray();

    //await _dispatcher.DispatchAndClearEvents(entitiesWithEvents);

    //return result;

    int result;

    if (CurrentTransaction == null)
    {
      CurrentTransaction = await this.BeginTransactionAsync(cancellationToken);
      await using (CurrentTransaction)
      {
        try
        {
          // ensure field 'Id' initialized when new entity added
          result = await base.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
          if (_dispatcher == null) return result;
          //await _dispatcher.DispatchAndClearEvents(entitiesWithEvents);
          await _dispatcher.DispatchAndClearEvents(this);
          await CommitAsync(cancellationToken);
          return result;
        }
        catch
        {
          await RollbackAsync(cancellationToken);
          throw;
        }
      }
    }
    else
    {
      result = await base.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
      if (_dispatcher == null) return result;
      //await _dispatcher.DispatchAndClearEvents(entitiesWithEvents);
      await _dispatcher.DispatchAndClearEvents(this);
      return result;
    }

  }

  public override int SaveChanges() =>
        SaveChangesAsync().GetAwaiter().GetResult();
}
