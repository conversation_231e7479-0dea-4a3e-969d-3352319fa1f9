﻿using MediatR;
using Microsoft.Extensions.Logging;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.Core.Platform.Services;

/// <summary>
/// 菜单领域服务实现
/// </summary>
public class MenuService : IMenuService
{
  private readonly IRepository<Menu> _menuRepository;
  private readonly IRepository<User> _userRepository;
  private readonly IRepository<Role> _roleRepository;
  private readonly IRepository<Permission> _permissionRepository;
  private readonly IMediator _mediator;
  private readonly ILogger<MenuService> _logger;

  public MenuService(
      IRepository<Menu> menuRepository,
      IRepository<User> userRepository,
      IRepository<Role> roleRepository,
      IRepository<Permission> permissionRepository,
      IMediator mediator,
      ILogger<MenuService> logger)
  {
    _menuRepository = menuRepository;
    _userRepository = userRepository;
    _roleRepository = roleRepository;
    _permissionRepository = permissionRepository;
    _mediator = mediator;
    _logger = logger;
  }

  /// <inheritdoc />
  public async Task<Result<Menu>> CreateMenuAsync(
      string menuName,
      MenuType menuType,
      string? authCode = null,
      Guid? parentId = null,
      int orderNum = 0,
      string? icon = null,
      string? router = null,
      string? component = null,
      string? routerName = null,
      string? redirect = null,
      string? query = null,
      string? remark = null,
      string? title = null,
      bool affixTab = false,
      int? affixTabOrder = null,
      string? badge = null,
      string? badgeType = null,
      string? badgeVariants = null,
      string? iframeSrc = null,
      string? link = null,
      bool? openInNewWindow = null,
      bool? keepAlive = null,
      bool? hideInMenu = null,
      bool? hideInTab = null,
      bool? hideInBreadcrumb = null,
      bool? hideChildrenInMenu = null,
      string? activePath = null,
      int? maxNumofOpenTab = null,
      bool? noBasicLayout = null)
  {
    try
    {
      // 创建新菜单
      Menu menu;
      Menu? parentMenu = null;
      if (parentId.HasValue && parentId.Value != Guid.Empty)
      {
        // 检查父菜单是否存在
        parentMenu = await _menuRepository.FirstOrDefaultAsync(new MenusWithPermissionSpec(parentId.Value));
        if (parentMenu == null)
        {
          return Result.Error("父菜单不存在");
        }
        menu = new Menu(parentId.Value)
        {
          MenuName = menuName
        };
      }
      else
      {
        menu = new Menu()
        {
          MenuName = menuName,
          ParentId = null
        };
      }

      // 设置其他属性
      menu.MenuType = menuType;
      menu.OrderNum = orderNum;
      menu.MenuIcon = icon;
      menu.Router = router;
      menu.Component = component;
      menu.RouterName = routerName;
      menu.Redirect = redirect;
      menu.Query = query;
      menu.Remark = remark;
      menu.Title = title;
      menu.AffixTab = affixTab;
      menu.AffixTabOrder = affixTabOrder;
      menu.Badge = badge;
      menu.BadgeType = badgeType;
      menu.BadgeVariants = badgeVariants;
      menu.IframeSrc = iframeSrc;
      menu.Link = link;
      menu.OpenInNewWindow = openInNewWindow ?? false;
      menu.KeepAlive = keepAlive ?? false;
      menu.HideInMenu = hideChildrenInMenu ?? false;
      menu.HideInTab = hideInTab ?? false;
      menu.HideInBreadcrumb = hideInBreadcrumb ?? false;
      menu.HideChildrenInMenu = hideChildrenInMenu ?? false;
      menu.ActivePath = activePath;
      menu.MaxNumOfOpenTab = maxNumofOpenTab;
      menu.NoBasicLayout = noBasicLayout;

      // 如果提供了authCode，创建对应的权限
      //if (menu.MenuType == MenuType.Action)

      if (menu.MenuType == MenuType.Catalog)
      {
        authCode = menu.MenuName;
      }

      if (menu.MenuType == MenuType.Menu)
      {
        if (string.IsNullOrEmpty(authCode) && parentMenu != null)
        { 
          authCode = $"{parentMenu.Permission?.Code}.{menu.MenuName}";
        }
      }

      if (!string.IsNullOrEmpty(authCode))
      {
        var permission = new Permission(authCode, menuName, PermissionType.Element);
        menu.Permission = permission;
      }

      // 保存菜单
      var createdMenu = await _menuRepository.AddAsync(menu);

      // 发布菜单创建事件
      await _mediator.Publish(new MenuCreatedEvent(createdMenu.Id, createdMenu.MenuName));

      return Result.Success(createdMenu);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"创建菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Menu>> UpdateMenuAsync(
      Guid menuId,
      string menuName,
      MenuType menuType,
      string? authCode = null,
      Guid? parentId = null,
      int orderNum = 0,
      string? icon = null,
      string? router = null,
      string? component = null,
      string? routerName = null,
      string? redirect = null,
      string? query = null,
      string? remark = null,
      string? title = null,
      bool affixTab = false,
      int? affixTabOrder = null,
      string? badge = null,
      string? badgeType = null,
      string? badgeVariants = null,
      string? iframeSrc = null,
      string? link = null,
      bool? openInNewWindow = null,
      bool? keepAlive = null,
      bool? hideInMenu = null,
      bool? hideInTab = null,
      bool? hideInBreadcrumb = null,
      bool? hideChildrenInMenu = null,
      string? activePath = null,
      int? maxNumofOpenTab = null,
      bool? noBasicLayout = null)
  {
    try
    { 
      var menu = await _menuRepository.FirstOrDefaultAsync(new MenusWithPermissionSpec(menuId));
      if (menu == null)
      {
        return Result.NotFound("菜单不存在");
      }

      // 检查父菜单是否存在且不是自己
      Menu? parentMenu = null; 
      if (parentId.HasValue && parentId.Value != Guid.Empty)
      {
        if (parentId.Value == menuId)
        {
          return Result.Error("父菜单不能是自己");
        }

        parentMenu = await _menuRepository.FirstOrDefaultAsync(new MenusWithPermissionSpec(parentId.Value));
        Guard.Against.Null(parentMenu, nameof(parentMenu), "父菜单不存在");

        // 检查是否会形成循环引用
        if (await IsChildMenuAsync(menuId, parentId.Value))
        {
          return Result.Error("不能将子菜单设为父菜单");
        }

        menu.ParentId = parentId.Value;
      }
      else
      {
        menu.ParentId = null;
      }

      // 更新菜单信息
      menu.MenuName = Guard.Against.NullOrEmpty(menuName, nameof(menuName));
      menu.MenuType = menuType;
      menu.OrderNum = orderNum;
      menu.MenuIcon = icon;
      menu.Router = router;
      menu.Component = component;
      menu.RouterName = routerName;
      menu.Redirect = redirect;
      menu.Query = query;
      menu.Remark = remark;
      menu.Title = title;
      menu.AffixTab = affixTab;
      menu.AffixTabOrder = affixTabOrder;
      menu.Badge = badge;
      menu.BadgeType = badgeType;
      menu.BadgeVariants = badgeVariants;
      menu.IframeSrc = iframeSrc;
      menu.Link = link;
      menu.OpenInNewWindow = openInNewWindow ?? false;
      menu.KeepAlive = keepAlive ?? false;
      menu.HideInMenu = hideChildrenInMenu ?? false;
      menu.HideInTab = hideInTab ?? false;
      menu.HideInBreadcrumb = hideInBreadcrumb ?? false;
      menu.HideChildrenInMenu = hideChildrenInMenu ?? false;
      menu.ActivePath = activePath;
      menu.MaxNumOfOpenTab = maxNumofOpenTab;
      menu.NoBasicLayout = noBasicLayout;

      if (menu.MenuType == MenuType.Catalog)
      {
        authCode = menu.MenuName;
      }

      
      if (menu.MenuType == MenuType.Menu)
      {
        if (string.IsNullOrEmpty(authCode) && parentMenu != null)
        {
          authCode = $"{parentMenu.Permission?.Code}.{menu.MenuName}";
        }
      }

      // 处理权限关联
      if (!string.IsNullOrEmpty(authCode))
      {
        if (menu.PermissionId.HasValue)
        {
          var permission = await _permissionRepository.GetByIdAsync(menu.PermissionId.Value);
          if (permission != null)
          {
            menu.Permission = permission;
          }
        }

        // 如果菜单没有关联的权限，则创建一个新的权限并关联
        if (menu.Permission == null)
        {
          var permission = new Permission(authCode, menuName, PermissionType.Element);
          await _permissionRepository.AddAsync(permission);
          menu.Permission = permission;
        }
        // 如果菜单已有关联的权限，则更新权限信息
        else if (authCode != menu.Permission.Code.ToString())
        {
          menu.Permission.Update(authCode, menuName, null, PermissionType.Element);
        }
      }
      // 如果没有提供权限编码但菜单已有关联权限，保持原有关联不变
      // 权限关系配置了级联删除，无需在此处处理

      await _menuRepository.UpdateAsync(menu);

      // 发布菜单更新事件
      await _mediator.Publish(new MenuUpdatedEvent(menu.Id, menu.MenuName));

      return Result.Success(menu);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"更新菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeleteMenuAsync(Guid menuId)
  {
    try
    {
      var menu = await _menuRepository.GetByIdAsync(menuId);
      if (menu == null)
      {
        return Result.NotFound("菜单不存在");
      }

      // 检查是否有子菜单
      var childMenus = await GetChildMenuAsync(menuId);
      if (childMenus.IsSuccess && childMenus.Value.Count > 0)
      {
        return Result.Error("请先删除子菜单");
      }

      // 检查权限是否被其他角色使用
      if (menu.Permission != null)
      {
        var rolePermissions = await _roleRepository.ListAsync(
            new RolePermissionsByPermissionIdSpec(menu.Permission.Id));

        if (rolePermissions.Any())
        {
          return Result.Error("该菜单的权限已被角色使用，请先解除角色权限关联");
        }
      }

      // 删除菜单（权限会通过级联删除自动删除）
      await _menuRepository.DeleteAsync(menu);

      // 发布菜单删除事件
      await _mediator.Publish(new MenuDeletedEvent(menuId, menu.MenuName));

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"删除菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Menu>> GetMenuAsync(Guid menuId)
  {
    try
    {
      var menu = await _menuRepository.GetByIdAsync(menuId);
      if (menu == null)
      {
        return Result.NotFound("菜单不存在");
      }

      return Result.Success(menu);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"获取菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Menu>>> GetMenuTreeAsync(Guid? parentId = null)
  {
    try
    {
      // 获取所有激活状态的菜单，一次性查询数据库
      var spec = new AllMenusSpec(true);
      var allMenus = await _menuRepository.ListAsync(spec);

      // 将菜单列表转换为字典，便于快速查找
      var menuDict = allMenus.ToDictionary(m => m.Id);

      // 构建菜单树
      var menuTree = BuildMenuTree(allMenus, parentId);

      if (parentId.HasValue && parentId.Value != Guid.Empty)
      {
        // 如果指定了父菜单ID，检查父菜单是否存在
        if (!menuDict.ContainsKey(parentId.Value))
        {
          return Result.NotFound("父菜单不存在");
        }

        // 返回指定父菜单下的菜单树
        return Result.Success(menuTree);
      }
      else
      {
        // 返回所有顶级菜单及其子菜单
        return Result.Success(menuTree);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取菜单树时发生错误: {Message}", ex.Message);
      return Result.Error($"获取菜单树失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 构建菜单树
  /// </summary>
  /// <param name="allMenus">所有菜单</param>
  /// <param name="parentId">父菜单ID</param>
  /// <returns>菜单树</returns>
  private static List<Menu> BuildMenuTree(IEnumerable<Menu> allMenus, Guid? parentId)
  {
    // 获取指定父ID的菜单
    var rootMenus = allMenus.Where(m => m.ParentId == parentId).ToList();
    if (rootMenus.Count == 0)
    {
      return [];
    }

    // 递归构建菜单树
    foreach (var menu in rootMenus)
    {
      menu.Children = BuildMenuTree(allMenus, menu.Id);
    }

    return rootMenus;
  }

  /// <inheritdoc />
  public async Task<Result<List<Menu>>> GetChildMenuAsync(Guid parentId)
  {
    try
    {
      var spec = new MenusByParentIdSpec(parentId);
      var menus = await _menuRepository.ListAsync(spec);
      return Result.Success(menus.ToList());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取子菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"获取子菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Menu>>> GetUserMenusAsync(Guid userId)
  {
    try
    {
      // 获取用户的所有角色
      var spec = new UserWithRolesSpec(userId);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      // 获取所有角色的菜单
      var menus = new List<Menu>();
      var userRoles = user.UserRoles.Select(l => l.Role).ToList();
      if (userRoles != null)
      {
        foreach (var role in userRoles)
        {
          var roleSpec = new RoleWithMenusSpec(role.Id);
          var roleWithMenus = await _roleRepository.FirstOrDefaultAsync(roleSpec);
          var roleMenus = roleWithMenus?.Permissions.Select(p => p.Menu!).ToList();
          if(roleMenus != null)
          {
            menus.AddRange(roleMenus);
          }
        }
      }

      // 去重
      var distinctMenus = menus.DistinctBy(m => m.Id).ToList();

      // 获取所有激活状态的菜单，用于构建完整的菜单树
      var allMenusSpec = new AllMenusSpec(true);
      var allMenus = await _menuRepository.ListAsync(allMenusSpec);

      // 过滤出用户有权限的菜单
      var userMenuIds = distinctMenus.Select(m => m.Id).ToHashSet();
      var filteredMenus = allMenus.Where(m => userMenuIds.Contains(m.Id) && m.MenuType != MenuType.Action).ToList();

      // 构建菜单树
      var menuTree = BuildMenuTree(filteredMenus, null);

      return Result.Success(menuTree);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"获取用户菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> ActivateMenuAsync(Guid menuId)
  {
    try
    {
      var menu = await _menuRepository.GetByIdAsync(menuId);
      if (menu == null)
      {
        return Result.NotFound("菜单不存在");
      }

      menu.State = MenuState.Activate;
      await _menuRepository.UpdateAsync(menu);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "激活菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"激活菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeactivateMenuAsync(Guid menuId)
  {
    try
    {
      var menu = await _menuRepository.GetByIdAsync(menuId);
      if (menu == null)
      {
        return Result.NotFound("菜单不存在");
      }

      menu.State = MenuState.Deactivate;
      await _menuRepository.UpdateAsync(menu);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "禁用菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"禁用菜单失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Permission>>> GetMenuPermissionsAsync(Guid menuId)
  {
    try
    {
      var spec = new PermissionsByMenuIdSpec(menuId);
      var permissions = await _permissionRepository.ListAsync(spec);
      return Result.Success(permissions.ToList());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取菜单权限时发生错误: {Message}", ex.Message);
      return Result.Error($"获取菜单权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Menu>>> GetUserMenuRoutesAsync(Guid userId)
  {
    try
    {
      // 获取用户的菜单
      var userMenusResult = await GetUserMenusAsync(userId);
      if (!userMenusResult.IsSuccess)
      {
        return Result.Error(new ErrorList(userMenusResult.Errors));
      }

      // 获取所有激活状态的菜单
      var allMenusSpec = new AllMenusSpec(true);
      var allMenus = await _menuRepository.ListAsync(allMenusSpec);

      // 过滤出用户有权限的菜单
      var userMenuIds = userMenusResult.Value.Select(m => m.Id).ToHashSet();

      // 构建菜单树
      var menuTree = BuildMenuTree(allMenus.Where(m => userMenuIds.Contains(m.Id)), null);

      return Result.Success(menuTree);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户菜单路由时发生错误: {Message}", ex.Message);
      return Result.Error($"获取用户菜单路由失败: {ex.Message}");
    }
  }

  public async Task<Result<bool>> CheckNameExists(Guid? menuId, string menuName, Guid? parentId)
  {
    try
    {
      // 获取所有同名的菜单
      var sameMenuNameSpec = new MenusByNameSpec(menuId, menuName, parentId);
      var result = await _menuRepository.AnyAsync(sameMenuNameSpec);

      return Result.Success(result);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取所有同名的菜单时发生错误: {Message}", ex.Message);
      return Result.Error($"获取所有同名的菜单失败: {ex.Message}");
    }
  }

  // 辅助方法：检查是否是子菜单
  private async Task<bool> IsChildMenuAsync(Guid parentId, Guid childId)
  {
    var childMenus = await GetChildMenuAsync(parentId);
    if (!childMenus.IsSuccess || childMenus.Value.Count == 0)
    {
      return false;
    }

    if (childMenus.Value.Any(m => m.Id == childId))
    {
      return true;
    }

    foreach (var menu in childMenus.Value)
    {
      if (await IsChildMenuAsync(menu.Id, childId))
      {
        return true;
      }
    }

    return false;
  }
}
