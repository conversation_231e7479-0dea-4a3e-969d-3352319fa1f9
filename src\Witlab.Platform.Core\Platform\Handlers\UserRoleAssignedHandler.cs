﻿using MediatR;
using Microsoft.Extensions.Logging;
using Witlab.Platform.Core.Common.Interfaces;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.WitLab.Interfaces;

namespace Witlab.Platform.Core.Platform.Handlers;

/// <summary>
/// 用户删除事件处理器
/// </summary>
public class UserRoleAssignedHandler : INotificationHandler<UserRoleAssignedEvent>
{
  private readonly ILogger<UserRoleAssignedHandler> _logger;
  private readonly IWitLabSyncService _witLabSyncService;

  public UserRoleAssignedHandler(ILogger<UserRoleAssignedHandler> logger, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(UserRoleAssignedEvent notification, CancellationToken cancellationToken)
  {
    await _witLabSyncService.SyncUserRolesAsync(notification.UserName, notification.RoleCodes);
  }
}
