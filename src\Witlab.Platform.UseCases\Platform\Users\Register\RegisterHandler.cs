﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.Register;

/// <summary>
/// 用户注册命令处理器
/// </summary>
public class RegisterHandler : ICommandHandler<RegisterCommand, Result<UserDTO>>
{
    private readonly IUserService _userService;
    private readonly IRoleService _roleService;

    public RegisterHandler(IUserService userService, IRoleService roleService)
    {
        _userService = userService;
        _roleService = roleService;
    }

    public async Task<Result<UserDTO>> Handle(RegisterCommand request, CancellationToken cancellationToken)
    {
        // 验证用户名是否已存在
        var existingUserResult = await _userService.GetUserByNameAsync(request.UserName);
        if (existingUserResult.IsSuccess)
        {
            return Result.Error("用户名已存在");
        }

        // 创建用户
        var result = await _userService.CreateUserAsync(
            request.UserName,
            request.Password,
            request.FullName,
            [],
            [],
            request.Email
        );

        if (!result.IsSuccess)
        {
            return Result.Error(new ErrorList(result.Errors));
        }

        var user = result.Value;

        // 分配默认角色（如果有）
        try
        {
            // 查找默认角色（如"user"角色）
            var defaultRoleResult = await _roleService.GetRoleByCodeAsync("user");
            if (defaultRoleResult.IsSuccess)
            {
                var defaultRole = defaultRoleResult.Value;
                await _userService.AssignRolesToUserAsync(user.Id, new List<Guid> { defaultRole.Id });
            }
        }
        catch (Exception)
        {
            // 如果分配默认角色失败，不影响注册流程
        }

        return Result.Success(MapToDto(user));
    }

    private static UserDTO MapToDto(User user)
    {
        return new UserDTO(
            user.Id,
            user.UserName,
            user.FullName,
            user.Email,
            user.Phone,
            user.Address,
            user.Icon,
            user.Sex.Value,
            user.Sex.Name,
            user.State.Value,
            user.State.Name,
            user.Remark,
            user.UserRoles.Select(l => l.Role).Select(r => r.RoleCode).ToList() ?? [],
            user.UserDepts.Select(l => l.Dept).Select(r => r.DeptCode).ToList() ?? [],
            user.Created.DateTime,
            user.CreatedBy,
            user.LastModified.DateTime,
            user.LastModifiedBy
        );
    }
}
