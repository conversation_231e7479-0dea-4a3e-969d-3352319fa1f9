﻿using Microsoft.EntityFrameworkCore;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
  public void Configure(EntityTypeBuilder<User> builder)
  {
    builder.HasKey(x => x.Id);

    // 配置审计字段
    builder.ConfigureAuditableProperties<User, Guid>();

    builder.Property(p => p.UserName)
        .HasMaxLength(50)
        .IsRequired();

    builder.Property(p => p.FullName)
        .HasMaxLength(50)
        .IsRequired();

    builder.Property(p => p.Email)
        .HasMaxLength(100);

    // 调整Icon字段为可存储base64字符串
    builder.Property(p => p.Icon)
        .HasMaxLength(4000); // 增加长度以支持较长的base64字符串

    builder.Property(p => p.Ip)
        .HasMaxLength(50);

    builder.Property(p => p.Address)
        .HasMaxLength(200);

    builder.Property(p => p.Remark)
        .HasMaxLength(500);

    // 配置值对象 EncryPassword
    builder.OwnsOne(p => p.EncryPassword, passwordBuilder =>
    {
      passwordBuilder.Property(p => p.Password)
              .HasColumnName("Password")
              .HasMaxLength(100)
              .IsRequired();

      passwordBuilder.Property(p => p.Salt)
              .HasColumnName("Salt")
              .HasMaxLength(50);
    });

    // 配置Sex SmartEnum转换
    builder.Property(p => p.Sex)
        .HasConversion(
            v => v.Value,
            v => Sex.FromValue(v));

    // 配置UserState SmartEnum转换
    builder.Property(p => p.State)
        .HasConversion(
            v => v.Value,
            v => UserState.FromValue(v));

    // 配置与Role的多对多关系
    builder
        .HasMany<UserRole>("_userRoles")
        .WithOne(ur => ur.User)
        .HasForeignKey(ur => ur.UserId)
        .OnDelete(DeleteBehavior.Cascade);

    //builder.HasMany(u => u.Roles)
    //        .WithMany(r => r.Users)
    //        .UsingEntity<UserRole>(
    //            join => join
    //                .HasOne(ur => ur.Role)
    //                .WithMany(r => r.UserRoles)
    //                .HasForeignKey(ur => ur.RoleId),
    //            join => join
    //                .HasOne(ur => ur.User)
    //                .WithMany(u => u.UserRoles)
    //                .HasForeignKey(ur => ur.UserId),
    //            join =>
    //            {
    //              join.HasKey(ur => new { ur.UserId, ur.RoleId });
    //              join.ToTable("UserRoles");
    //            });

    // 配置与Dept的多对多关系
    builder
        .HasMany<UserDept>("_userDepts")
        .WithOne(ud => ud.User)
        .HasForeignKey(ud => ud.UserId)
        .OnDelete(DeleteBehavior.Cascade);

    //builder
    //  .HasMany(typeof(UserDept), "_userDepts") // 映射私有字段
    //  .WithOne()
    //  .HasForeignKey("UserId");

    //builder.HasMany(u => u.Depts)
    //    .WithMany(d => d.Users)
    //    .UsingEntity<UserDept>(
    //        join => join
    //            .HasOne(ud => ud.Dept)
    //            .WithMany(d => d.UserDepts)
    //            .HasForeignKey(ud => ud.DeptId),
    //        join => join
    //            .HasOne(ud => ud.User)
    //            .WithMany(u => u.UserDepts)
    //            .HasForeignKey(ud => ud.UserId),
    //        join =>
    //        {
    //          join.HasKey(ud => new { ud.UserId, ud.DeptId });
    //          join.ToTable("UserDepts");
    //        });

    builder.ToTable("Users");
  }
}
