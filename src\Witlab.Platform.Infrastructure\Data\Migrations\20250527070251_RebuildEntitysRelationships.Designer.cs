﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Witlab.Platform.Infrastructure.Data;

#nullable disable

namespace Witlab.Platform.Infrastructure.Data.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250527070251_RebuildEntitysRelationships")]
    partial class RebuildEntitysRelationships
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.5");

            modelBuilder.Entity("Witlab.Platform.Core.Auth.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Token")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.ContributorAggregate.Contributor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("Contributors");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Dept", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("DeptCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("DeptName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Leader")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("OrderNum")
                        .HasColumnType("INTEGER");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("State")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("IX_Depts_ParentId");

                    b.ToTable("Depts", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Menu", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("ActiveIcon")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("ActivePath")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("AffixTab")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AffixTabOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Badge")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("BadgeType")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("BadgeVariants")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Component")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("HideChildrenInMenu")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HideInBreadcrumb")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HideInMenu")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HideInTab")
                        .HasColumnType("INTEGER");

                    b.Property<string>("IframeSrc")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("KeepAlive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Link")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("MaxNumOfOpenTab")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MenuIcon")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("MenuName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("MenuType")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("NoBasicLayout")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("OpenInNewWindow")
                        .HasColumnType("INTEGER");

                    b.Property<int>("OrderNum")
                        .HasColumnType("INTEGER");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("PermissionId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Query")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Redirect")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Router")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("RouterName")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("State")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("IX_Menus_ParentId");

                    b.HasIndex("PermissionId")
                        .IsUnique();

                    b.ToTable("Menus", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Permissions", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("MenuId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("State")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MenuId");

                    b.ToTable("Roles", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.RolePermission", b =>
                {
                    b.Property<Guid>("RoleId")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("Id")
                        .HasColumnType("TEXT");

                    b.HasKey("RoleId", "PermissionId");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId");

                    b.ToTable("RolePermissions");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("DeptId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Icon")
                        .HasMaxLength(4000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Ip")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<long?>("Phone")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Remark")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("TEXT");

                    b.Property<int>("Sex")
                        .HasColumnType("INTEGER");

                    b.Property<int>("State")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DeptId");

                    b.HasIndex("RoleId");

                    b.ToTable("Users", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.UserDept", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("DeptId")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("Id")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "DeptId");

                    b.HasIndex("DeptId");

                    b.ToTable("UserDepts", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.UserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("TEXT");

                    b.Property<Guid>("Id")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRoles", (string)null);
                });

            modelBuilder.Entity("Witlab.Platform.Core.ContributorAggregate.Contributor", b =>
                {
                    b.OwnsOne("Witlab.Platform.Core.ContributorAggregate.PhoneNumber", "PhoneNumber", b1 =>
                        {
                            b1.Property<int>("ContributorId")
                                .HasColumnType("INTEGER");

                            b1.Property<string>("CountryCode")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.Property<string>("Extension")
                                .HasColumnType("TEXT");

                            b1.Property<string>("Number")
                                .IsRequired()
                                .HasColumnType("TEXT");

                            b1.HasKey("ContributorId");

                            b1.ToTable("Contributors");

                            b1.WithOwner()
                                .HasForeignKey("ContributorId");
                        });

                    b.Navigation("PhoneNumber");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Dept", b =>
                {
                    b.HasOne("Witlab.Platform.Core.Platform.Dept", null)
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Menu", b =>
                {
                    b.HasOne("Witlab.Platform.Core.Platform.Menu", null)
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Witlab.Platform.Core.Platform.Permission", "Permission")
                        .WithOne("Menu")
                        .HasForeignKey("Witlab.Platform.Core.Platform.Menu", "PermissionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Role", b =>
                {
                    b.HasOne("Witlab.Platform.Core.Platform.Menu", null)
                        .WithMany("Roles")
                        .HasForeignKey("MenuId");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.RolePermission", b =>
                {
                    b.HasOne("Witlab.Platform.Core.Platform.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Witlab.Platform.Core.Platform.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.User", b =>
                {
                    b.HasOne("Witlab.Platform.Core.Platform.Dept", null)
                        .WithMany("Users")
                        .HasForeignKey("DeptId");

                    b.HasOne("Witlab.Platform.Core.Platform.Role", null)
                        .WithMany("Users")
                        .HasForeignKey("RoleId");

                    b.OwnsOne("Witlab.Platform.Core.Platform.EncryPassword", "EncryPassword", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("TEXT");

                            b1.Property<string>("Password")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("TEXT")
                                .HasColumnName("Password");

                            b1.Property<string>("Salt")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("TEXT")
                                .HasColumnName("Salt");

                            b1.HasKey("UserId");

                            b1.ToTable("Users");

                            b1.WithOwner()
                                .HasForeignKey("UserId");
                        });

                    b.Navigation("EncryPassword")
                        .IsRequired();
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.UserDept", b =>
                {
                    b.HasOne("Witlab.Platform.Core.Platform.Dept", "Dept")
                        .WithMany("UserDepts")
                        .HasForeignKey("DeptId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Witlab.Platform.Core.Platform.User", "User")
                        .WithMany("UserDepts")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Dept");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.UserRole", b =>
                {
                    b.HasOne("Witlab.Platform.Core.Platform.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Witlab.Platform.Core.Platform.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Dept", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("UserDepts");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Menu", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Roles");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Permission", b =>
                {
                    b.Navigation("Menu");

                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.Role", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoles");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Witlab.Platform.Core.Platform.User", b =>
                {
                    b.Navigation("UserDepts");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
