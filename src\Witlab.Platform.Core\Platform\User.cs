using Witlab.Platform.Core.Platform.Events;
using WitLab.Platform.SharedKernel.Helper;

namespace Witlab.Platform.Core.Platform;
public class User : AuditableEntityBase<Guid>, IAggregateRoot
{
  /// <summary>
  /// 用户名
  /// </summary>
  public string UserName { get; init; } = string.Empty;
  /// <summary>
  /// 姓名
  /// </summary>
  public string FullName { get; set; } = string.Empty;
  /// <summary>
  /// Email
  /// </summary>
  public string? Email { get; set; }
  /// <summary>
  /// 密码
  /// </summary>
  public EncryPassword EncryPassword { get; private set; } = new EncryPassword();
  /// <summary>
  /// 头像base64
  /// </summary>
  public string? Icon { get; set; }
  /// <summary>
  /// IP
  /// </summary>
  public string? Ip { get; set; }
  /// <summary>
  /// 地址
  /// </summary>
  public string? Address { get; set; }
  /// <summary>
  /// 联系电话
  /// </summary>
  public long? Phone { get; set; }
  /// <summary>
  /// 性别
  /// </summary>
  public Sex Sex { get; set; } = Sex.Unknown;
  /// <summary>
  /// 状态
  /// </summary>
  public UserState State { get; set; } = UserState.Activate;
  /// <summary>
  /// 备注
  /// </summary>
  public string? Remark { get; set; }

  //public List<Role>? Roles { get; private set; }
  //public List<Dept>? Depts { get; private set; }

  //public ICollection<UserDept> UserDepts { get; private set; } = new List<UserDept>();
  private List<UserDept> _userDepts = new();
  public IReadOnlyCollection<UserDept> UserDepts => _userDepts;

  //public ICollection<UserRole> UserRoles { get; private set; } = new List<UserRole>();
  private List<UserRole> _userRoles = new();
  public IReadOnlyCollection<UserRole> UserRoles => _userRoles;

  private User()
  {
    // for efcore 
  }
  public User(string userName, string password, string fullName)
  {
    UserName = Guard.Against.NullOrEmpty(userName, nameof(userName));
    EncryPassword.Password = Guard.Against.NullOrEmpty(password, nameof(password));
    FullName = Guard.Against.NullOrEmpty(fullName, nameof(fullName));

    this.RegisterDomainEvent(new UserCreatedEvent(Id, userName, fullName, password));
  }

  /// <summary>
  /// 构建密码，MD5盐值加密
  /// </summary>
  public User BuildPassword(string? password = null)
  {
    //如果不传值，那就把自己的password当作传进来的password
    if (password == null)
    {
      Guard.Against.NullOrEmpty(EncryPassword.Password, nameof(EncryPassword.Password));

      password = EncryPassword.Password;
    }
    EncryPassword.Salt = MD5Helper.GenerateSalt();
    EncryPassword.Password = MD5Helper.SHA2Encode(password, EncryPassword.Salt);
    return this;
  }

  /// <summary>
  /// 判断密码和加密后的密码是否相同
  /// </summary>
  /// <param name="password"></param>
  /// <returns></returns>
  public bool JudgePassword(string password)
  {
    if (EncryPassword.Salt is null)
    {
      throw new ArgumentNullException(EncryPassword.Salt);
    }
    var p = MD5Helper.SHA2Encode(password, EncryPassword.Salt);
    if (EncryPassword.Password == MD5Helper.SHA2Encode(password, EncryPassword.Salt))
    {
      return true;
    }
    return false;
  }

  public void AssignDepts(List<Dept> depts)
  {
    _userDepts.Clear();
    foreach (var dept in depts)
    {
      _userDepts.Add(new UserDept(Id, dept.Id));
    }
    RegisterDomainEvent(new UserDeptAssignedEvent(UserName, depts.Select(d => d.DeptCode).ToList()));
  }

  public void AssignRoles(List<Role> roles)
  {
    _userRoles.Clear();
    foreach (var role in roles)
    {
      _userRoles.Add(new UserRole(Id, role.Id)); 
    }
    RegisterDomainEvent(new UserRoleAssignedEvent(UserName, roles.Select(d => d.RoleCode).ToList()));
  }
}
