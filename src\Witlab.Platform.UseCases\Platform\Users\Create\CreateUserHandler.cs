﻿using System.Data;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.WitLab.Interfaces;

namespace Witlab.Platform.UseCases.Platform.Users.Create;

/// <summary>
/// 创建用户命令处理器
/// </summary>
public class CreateUserHandler : ICommandHandler<CreateUserCommand, Result<UserDTO>>
{
  private readonly IUserService _userService;
  private readonly IWitLabSyncService _witLabSyncService;

  public CreateUserHandler(IUserService userService, IWitLabSyncService witLabSyncService)
  {
    _userService = userService;
    _witLabSyncService = witLabSyncService;
  }

  public async Task<Result<UserDTO>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
  {
    var result = await _userService.CreateUserAsync(
        request.UserName,
        request.Password,
        request.FullName,
        request.Depts,
        request.Roles,
        request.Email,
        request.Phone,
        request.Address,
        request.Icon,
        request.SexValue,
        request.StateValue,
        request.Remark
    );

    if (!result.IsSuccess)
    {
      return Result.Error(new ErrorList(result.Errors));
    }

    var user = result.Value;

    //_ = await _witLabSyncService.SyncAddUserInfoAsync(user.UserName,user.FullName);
    //_ = await _witLabSyncService.SyncChangeUserPasswordAsync(user.UserName, request.Password);
    //if (request.Depts != null && request.Depts.Count() > 0)
    //{
    //  _ = await _witLabSyncService.SyncUserDeptsAsync(user.UserName, request.Depts);
    //}
    //if (request.Roles != null && request.Roles.Count() > 0)
    //{
    //  _ = await _witLabSyncService.SyncUserRolesAsync(user.UserName, request.Roles);
    //}

    return Result.Success(MapToDto(user));
  }

  private static UserDTO MapToDto(User user)
  {
    return new UserDTO(
        user.Id,
        user.UserName,
        user.FullName,
        user.Email,
        user.Phone,
        user.Address,
        user.Icon,
        user.Sex.Value,
        user.Sex.Name,
        user.State.Value,
        user.State.Name,
        user.Remark,
        user.UserRoles.Select(l=> l.Role).Select(r => r.RoleCode).ToList() ?? [],
        user.UserDepts.Select(l => l.Dept).Select(r => r.DeptCode).ToList() ?? [],
        user.Created.DateTime,
        user.CreatedBy,
        user.LastModified.DateTime,
        user.LastModifiedBy
    );
  }
}
