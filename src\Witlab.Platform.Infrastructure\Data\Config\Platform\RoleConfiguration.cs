﻿﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.HasKey(x => x.Id);

        // 配置审计字段
        builder.ConfigureAuditableProperties<Role, Guid>();

        builder.Property(p => p.RoleName)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(p => p.RoleCode)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(p => p.Remark)
            .HasMaxLength(500);

        // 配置State属性
        builder.Property(p => p.State);

        // 配置与User的多对多关系
        //builder.HasMany(r => r.Users)
        //    .WithMany(u => u.Roles)
        //    .UsingEntity<UserRole>(
        //        join => join
        //            .HasOne(ur => ur.User)
        //            .WithMany(u => u.UserRoles)
        //            .HasForeignKey(ur => ur.UserId),
        //        join => join
        //            .HasOne(ur => ur.Role)
        //            .WithMany(r => r.UserRoles)
        //            .HasForeignKey(ur => ur.RoleId),
        //        join =>
        //        {
        //            join.HasKey(ur => new { ur.UserId, ur.RoleId });
        //            join.ToTable("UserRoles");
        //        });

        // 配置与Permission的多对多关系
        builder.HasMany(r => r.Permissions)
            .WithMany(p => p.Roles) // 不直接指定导航属性，防止导航循环
            .UsingEntity<RolePermission>(
                join => join
                    .HasOne(rp => rp.Permission)
                    .WithMany(p => p.RolePermissions)
                    .HasForeignKey(rp => rp.PermissionId)
                    .OnDelete(DeleteBehavior.Cascade), // 删除权限时级联删除角色权限关联
                join => join
                    .HasOne(rp => rp.Role)
                    .WithMany(r => r.RolePermissions)
                    .HasForeignKey(rp => rp.RoleId)
                    .OnDelete(DeleteBehavior.Cascade), // 删除角色时级联删除角色权限关联
                join =>
                {
                    join.HasKey(rp => new { rp.RoleId, rp.PermissionId });
                    
                    // 添加索引以提高查询性能
                    join.HasIndex(rp => rp.PermissionId);
                    join.HasIndex(rp => rp.RoleId);
                });

        builder.ToTable("Roles");
    }
}
