﻿using System.Data;
using MediatR;
using Microsoft.Extensions.Logging;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.Core.Platform.Services;

/// <summary>
/// 权限领域服务实现
/// </summary>
public class PermissionService : IPermissionService
{
  private readonly IRepository<Permission> _permissionRepository;
  private readonly IRepository<Role> _roleRepository;
  private readonly IRepository<User> _userRepository;
  private readonly IRepository<Menu> _menuRepository;
  private readonly IMediator _mediator;
  private readonly ILogger<PermissionService> _logger;

  public PermissionService(
      IRepository<Permission> permissionRepository,
      IRepository<Role> roleRepository,
      IRepository<User> userRepository,
      IRepository<Menu> menuRepository,
      IMediator mediator,
      ILogger<PermissionService> logger)
  {
    _permissionRepository = permissionRepository;
    _roleRepository = roleRepository;
    _userRepository = userRepository;
    _menuRepository = menuRepository;
    _mediator = mediator;
    _logger = logger;
  }

  /// <inheritdoc />
  public async Task<Result<Permission>> CreatePermissionAsync(string code, string name, PermissionType type, string? description = null, Guid? menuId = null)
  {
    try
    {
      // 检查权限编码是否已存在
      var spec = new PermissionByCodeSpec(code);
      var existingPermission = await _permissionRepository.FirstOrDefaultAsync(spec);
      if (existingPermission != null)
      {
        return Result.Error("权限编码已存在");
      }

      // 创建新权限
      var permission = new Permission(code, name, type)
      {
        Description = description,
      };

      // 保存权限
      var createdPermission = await _permissionRepository.AddAsync(permission);

      // 如果提供了menuId，需要在Menu端设置PermissionId
      if (menuId.HasValue)
      {
        // 此处不处理Menu关联，因为在创建菜单时会自动关联权限
        // Menu关联关系应该在MenuService中处理
        _logger.LogInformation("创建权限时指定了MenuId {MenuId}，需要在Menu端进行关联", menuId.Value);
      }

      // 发布权限创建事件
      await _mediator.Publish(new PermissionCreatedEvent(createdPermission.Id, createdPermission.Code, createdPermission.Name));

      return Result.Success(createdPermission);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建权限时发生错误: {Message}", ex.Message);
      return Result.Error($"创建权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Permission>> UpdatePermissionAsync(Guid permissionId, string code, string name, PermissionType type, string? description, Guid? menuId)
  {
    try
    {
      var permission = await _permissionRepository.GetByIdAsync(permissionId);
      if (permission == null)
      {
        return Result.NotFound("权限不存在");
      }

      // 检查权限编码是否已被其他权限使用
      if (permission.Code != code)
      {
        var spec = new PermissionByCodeSpec(code);
        var existingPermission = await _permissionRepository.FirstOrDefaultAsync(spec);
        if (existingPermission != null && existingPermission.Id != permissionId)
        {
          return Result.Error("权限编码已存在");
        }
      }

      // 更新权限信息
      permission.Update(code, name, description, type);

      // 注意：在一对一关系中，Menu端有外键，所以不需要在这里设置Menu关系
      // 相关的关联操作应该在MenuService中处理

      await _permissionRepository.UpdateAsync(permission);

      // 发布权限更新事件
      await _mediator.Publish(new PermissionUpdatedEvent(permission.Id, permission.Code, permission.Name));

      return Result.Success(permission);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新权限时发生错误: {Message}", ex.Message);
      return Result.Error($"更新权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DeletePermissionAsync(Guid permissionId)
  {
    try
    {
      var permission = await _permissionRepository.GetByIdAsync(permissionId);
      if (permission == null)
      {
        return Result.NotFound("权限不存在");
      }

      // 检查是否有关联的菜单
      var menuSpec = new MenuByPermissionIdSpec(permissionId);
      var menu = await _menuRepository.FirstOrDefaultAsync(menuSpec);
      if (menu != null)
      {
        // 可以选择阻止删除或解除关联
        // 这里选择解除关联
        menu.PermissionId = null;
        await _menuRepository.UpdateAsync(menu);
        _logger.LogInformation("删除权限前解除了与菜单 {MenuId} 的关联", menu.Id);
      }

      await _permissionRepository.DeleteAsync(permission);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除权限时发生错误: {Message}", ex.Message);
      return Result.Error($"删除权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Permission>> GetPermissionAsync(Guid permissionId)
  {
    try
    {
      var permission = await _permissionRepository.GetByIdAsync(permissionId);
      if (permission == null)
      {
        return Result.NotFound("权限不存在");
      }

      return Result.Success(permission);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取权限时发生错误: {Message}", ex.Message);
      return Result.Error($"获取权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<Permission>> GetPermissionByCodeAsync(string code)
  {
    try
    {
      var spec = new PermissionByCodeSpec(code);
      var permission = await _permissionRepository.FirstOrDefaultAsync(spec);
      if (permission == null)
      {
        return Result.NotFound("权限不存在");
      }

      return Result.Success(permission);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据权限编码获取权限时发生错误: {Message}", ex.Message);
      return Result.Error($"获取权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Permission>>> GetPermissionsByMenuIdAsync(Guid menuId)
  {
    try
    {
      var spec = new PermissionsByMenuIdSpec(menuId);
      var permissions = await _permissionRepository.ListAsync(spec);
      return Result.Success(permissions.ToList());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据菜单ID获取权限列表时发生错误: {Message}", ex.Message);
      return Result.Error($"获取权限列表失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Permission>>> GetPermissionsByTypeAsync(PermissionType type)
  {
    try
    {
      var spec = new PermissionsByTypeSpec(type);
      var permissions = await _permissionRepository.ListAsync(spec);
      return Result.Success(permissions.ToList());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据权限类型获取权限列表时发生错误: {Message}", ex.Message);
      return Result.Error($"获取权限列表失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<bool>> ValidateUserPermissionAsync(Guid userId, string permissionCode)
  {
    try
    {
      // 获取用户的所有权限
      var userPermissionsResult = await GetUserPermissionsAsync(userId);
      if (!userPermissionsResult.IsSuccess)
      {
        return Result.Error(userPermissionsResult.Errors.FirstOrDefault() ?? "获取用户权限失败");
      }

      // 检查是否包含指定权限
      var hasPermission = userPermissionsResult.Value.Any(p => p.Code == permissionCode && p.IsEnabled);
      return Result.Success(hasPermission);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "验证用户权限时发生错误: {Message}", ex.Message);
      return Result.Error($"验证用户权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result<List<Permission>>> GetUserPermissionsAsync(Guid userId)
  {
    try
    {
      // 获取用户的所有角色
      var spec = new UserWithRolesSpec(userId);
      var user = await _userRepository.FirstOrDefaultAsync(spec);
      if (user == null)
      {
        return Result.NotFound("用户不存在");
      }

      // 获取所有角色的权限
      var permissions = new List<Permission>();
      var userRoles = user.UserRoles.Select(p => p.Role).ToList();
      if (userRoles != null)
      {
        foreach (var role in userRoles)
        {
          var roleSpec = new RoleWithPermissionsSpec(role.Id);
          var roleWithPermissions = await _roleRepository.FirstOrDefaultAsync(roleSpec);
          if (roleWithPermissions?.Permissions != null)
          {
            permissions.AddRange(roleWithPermissions.Permissions);
          }
        }
      }

      // 去重
      return Result.Success(permissions.DistinctBy(p => p.Id).ToList());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户权限时发生错误: {Message}", ex.Message);
      return Result.Error($"获取用户权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> EnablePermissionAsync(Guid permissionId)
  {
    try
    {
      var permission = await _permissionRepository.GetByIdAsync(permissionId);
      if (permission == null)
      {
        return Result.NotFound("权限不存在");
      }

      permission.Enable();
      await _permissionRepository.UpdateAsync(permission);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "启用权限时发生错误: {Message}", ex.Message);
      return Result.Error($"启用权限失败: {ex.Message}");
    }
  }

  /// <inheritdoc />
  public async Task<Result> DisablePermissionAsync(Guid permissionId)
  {
    try
    {
      var permission = await _permissionRepository.GetByIdAsync(permissionId);
      if (permission == null)
      {
        return Result.NotFound("权限不存在");
      }

      permission.Disable();
      await _permissionRepository.UpdateAsync(permission);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "禁用权限时发生错误: {Message}", ex.Message);
      return Result.Error($"禁用权限失败: {ex.Message}");
    }
  }
}
