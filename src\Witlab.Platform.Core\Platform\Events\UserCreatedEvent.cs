﻿using WitLab.Platform.SharedKernel;

namespace Witlab.Platform.Core.Platform.Events;

/// <summary>
/// 用户创建事件
/// </summary>
public class UserCreatedEvent : DomainEventBase
{
  public Guid UserId { get; }
  public string UserName { get; }
  public string FullName { get; }
  public string Password { get; }

  public UserCreatedEvent(Guid userId, string userName, string fullName, string password)
  {
    UserId = userId;
    UserName = userName;
    FullName = fullName;
    Password = password;
  } 
}
