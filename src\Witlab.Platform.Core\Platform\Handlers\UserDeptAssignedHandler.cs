﻿using MediatR;
using Microsoft.Extensions.Logging;
using Witlab.Platform.Core.Common.Interfaces;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.WitLab.Interfaces;

namespace Witlab.Platform.Core.Platform.Handlers;

/// <summary>
/// 用户删除事件处理器
/// </summary>
public class UserDeptAssignedHandler : INotificationHandler<UserDeptAssignedEvent>
{
  private readonly ILogger<UserDeptAssignedHandler> _logger;
  private readonly IWitLabSyncService _witLabSyncService;

  public UserDeptAssignedHandler(ILogger<UserDeptAssignedHandler> logger, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(UserDeptAssignedEvent notification, CancellationToken cancellationToken)
  {
    await _witLabSyncService.SyncUserDeptsAsync(notification.UserName, notification.DeptCodes, cancellationToken);
  }
}
