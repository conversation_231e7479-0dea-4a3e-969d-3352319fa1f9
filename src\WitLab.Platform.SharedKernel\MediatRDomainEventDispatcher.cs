﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace WitLab.Platform.SharedKernel;

public class MediatRDomainEventDispatcher : IDomainEventDispatcher
{
  private readonly IMediator _mediator;
  private readonly ILogger<MediatRDomainEventDispatcher> _logger;

  private const int Max_Deep = 10;

  public MediatRDomainEventDispatcher(IMediator mediator, ILogger<MediatRDomainEventDispatcher> logger)
  {
    _mediator = mediator;
    _logger = logger;
  }

  public async Task DispatchAndClearEvents(IEnumerable<IHasDomainEvents> entitiesWithEvents)
  {
    foreach (var entity in entitiesWithEvents)
    {
      if (entity is HasDomainEventsBase hasDomainEvents)
      {
        var events = hasDomainEvents.DomainEvents.ToArray();
        hasDomainEvents.ClearDomainEvents();

        foreach (var domainEvent in events)
          await _mediator.Publish(domainEvent).ConfigureAwait(false);
      }
      else
      {
        _logger.LogError(
          "Entity of type {EntityType} does not inherit from {BaseType}. Unable to clear domain events.",
          entity.GetType().Name,
          nameof(HasDomainEventsBase));
      }
    }
  }

  public async Task DispatchAndClearEvents(DbContext dbContext, int deep = 0,
      CancellationToken cancellationToken = default)
  {
    var entitiesWithEvents = dbContext.ChangeTracker.Entries<HasDomainEventsBase>()
        .Select(e => e.Entity)
        .Where(e => e.DomainEvents.Any())
        .ToList();

    var events = entitiesWithEvents.SelectMany(e => e.DomainEvents).ToList();

    if (deep > Max_Deep)
    {
      throw new Exception($"领域事件发布超过最大递归深度: {Max_Deep}");
    }

    if (events.Count() == 0)
    {
      return;
    }

    entitiesWithEvents.ForEach(entity => entity.ClearDomainEvents());

    foreach (var domainEvent in events)
    {
      try
      { 
        await _mediator.Publish(domainEvent, cancellationToken); 
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error while publishing domain event {EventType}", domainEvent.GetType().Name);
        throw;
      }
    }

    await DispatchAndClearEvents(dbContext, deep + 1, cancellationToken);
  } 
}
