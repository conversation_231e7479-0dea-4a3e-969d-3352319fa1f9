﻿<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />
  <ItemGroup>
    <Compile Remove="Platform\RoleMenu.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" />
    <PackageReference Include="Ardalis.Result" />
    <PackageReference Include="Ardalis.SmartEnum" />
    <PackageReference Include="Ardalis.Specification" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WitLab.Platform.SharedKernel\WitLab.Platform.SharedKernel.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\Services\" />
  </ItemGroup>

</Project>
