﻿using Microsoft.EntityFrameworkCore;
using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class UserRoleConfiguration : IEntityTypeConfiguration<UserRole>
{
  public void Configure(EntityTypeBuilder<UserRole> builder)
  {
    builder.HasKey(ur => new { ur.UserId, ur.RoleId });

    builder.Property(p => p.UserId)
        .IsRequired();

    builder.Property(p => p.RoleId)
        .IsRequired();

    builder.HasOne(ur => ur.User)
        .WithMany("_userRoles")
        .HasForeignKey(ur => ur.UserId)
        .OnDelete(DeleteBehavior.Cascade);

    builder.HasOne(ur => ur.Role)
        .WithMany(r => r.UserRoles)
        .HasForeignKey(ur => ur.RoleId)
        .OnDelete(DeleteBehavior.Cascade);

    builder.ToTable("UserRoles");
  }
}
