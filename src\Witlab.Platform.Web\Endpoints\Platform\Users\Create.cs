﻿using Witlab.Platform.UseCases.Platform.Users.Create;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 创建用户
/// </summary>
public class Create(IMediator _mediator) : Endpoint<CreateUserRequest, CreateUserResponse>
{
  public override void Configure()
  {
    Post(CreateUserRequest.Route);
    Description(x => x.AutoTagOverride("User"));
    AllowAnonymous();
    Summary(s =>
    {
      s.Summary = "创建新用户";
      s.Description = "创建一个新用户，需要提供用户名、密码和姓名";
      s.ExampleRequest = new CreateUserRequest { UserName = "newuser", Password = "Password123!", FullName = "新用户" };
    });
  }

  public override async Task HandleAsync(CreateUserRequest request, CancellationToken cancellationToken)
  {
    var command = new CreateUserCommand                                 (
        request.UserName!,
        request.Password!,
        request.FullName!,
        request.Email,
        request.Phone,
        request.Address,
        request.Icon,
        request.SexValue,
        request.StateValue,
        request.Remark,
        request.Depts,
        request.Roles
    );

    var result = await _mediator.Send(command, cancellationToken);

    if (result.IsSuccess)
    {
      var dto = result.Value;
      Response = new CreateUserResponse(
          dto.Id,
          dto.UserName,
          dto.FullName,
          dto.Email
      );
      return;
    }

    // 处理错误
    if (result.Status == ResultStatus.NotFound)
    {
      await SendNotFoundAsync();
      return;
    }

    await SendErrorsAsync();
  }
}
